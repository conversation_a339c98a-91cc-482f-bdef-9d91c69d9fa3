'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { useAuthStore } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  User, 
  Bell, 
  Shield, 
  Palette, 
  Bot,
  Key,
  Server,
  Plus,
  Trash2
} from 'lucide-react';

export default function Settings() {
  const { profile, updateProfile } = useAuthStore();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  // Profile settings
  const [fullName, setFullName] = useState(profile?.full_name || '');
  const [timezone, setTimezone] = useState(profile?.timezone || 'UTC');

  // Notification settings
  const [emailNotifications, setEmailNotifications] = useState(
    profile?.preferences?.notifications ?? true
  );

  // Theme settings
  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>(profile?.preferences?.theme || 'light');

  // Admin settings (mock data)
  const [apiKeys, setApiKeys] = useState([
    { id: '1', name: 'OpenRouter Key 1', masked: 'sk-...abc123', active: true },
    { id: '2', name: 'OpenRouter Key 2', masked: 'sk-...def456', active: false },
  ]);

  const [enabledModels, setEnabledModels] = useState([
    'claude-3-sonnet',
    'gpt-4',
    'claude-3-haiku'
  ]);

  const handleUpdateProfile = async () => {
    if (!profile) return;

    setLoading(true);
    try {
      await updateProfile({
        full_name: fullName,
        timezone,
        preferences: {
          ...profile.preferences,
          notifications: emailNotifications,
          theme,
        },
      });
      toast({
        title: 'Profile updated',
        description: 'Your profile has been updated successfully.',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update profile',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const isAdmin = profile?.role === 'admin';

  return (
    <DashboardLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Manage your account and application preferences</p>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          <div className="max-w-4xl mx-auto">
            <Tabs defaultValue="profile" className="space-y-6">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="profile" className="flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  Profile
                </TabsTrigger>
                <TabsTrigger value="notifications" className="flex items-center">
                  <Bell className="h-4 w-4 mr-2" />
                  Notifications
                </TabsTrigger>
                <TabsTrigger value="appearance" className="flex items-center">
                  <Palette className="h-4 w-4 mr-2" />
                  Appearance
                </TabsTrigger>
                <TabsTrigger value="ai" className="flex items-center">
                  <Bot className="h-4 w-4 mr-2" />
                  AI Settings
                </TabsTrigger>
                {isAdmin && (
                  <TabsTrigger value="admin" className="flex items-center">
                    <Shield className="h-4 w-4 mr-2" />
                    Admin
                  </TabsTrigger>
                )}
              </TabsList>

              {/* Profile Settings */}
              <TabsContent value="profile">
                <Card>
                  <CardHeader>
                    <CardTitle>Profile Information</CardTitle>
                    <CardDescription>
                      Update your personal information and account details.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="fullName">Full Name</Label>
                        <Input
                          id="fullName"
                          value={fullName}
                          onChange={(e) => setFullName(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          value={profile?.email || ''}
                          disabled
                          className="bg-gray-50"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="timezone">Timezone</Label>
                      <Select value={timezone} onValueChange={setTimezone}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="UTC">UTC</SelectItem>
                          <SelectItem value="America/New_York">Eastern Time</SelectItem>
                          <SelectItem value="America/Chicago">Central Time</SelectItem>
                          <SelectItem value="America/Denver">Mountain Time</SelectItem>
                          <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                          <SelectItem value="Europe/London">London</SelectItem>
                          <SelectItem value="Europe/Paris">Paris</SelectItem>
                          <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button onClick={handleUpdateProfile} disabled={loading}>
                      {loading ? 'Updating...' : 'Update Profile'}
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Notification Settings */}
              <TabsContent value="notifications">
                <Card>
                  <CardHeader>
                    <CardTitle>Notification Preferences</CardTitle>
                    <CardDescription>
                      Choose how you want to be notified about project updates.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Email Notifications</Label>
                        <p className="text-sm text-gray-600">
                          Receive email notifications for project updates
                        </p>
                      </div>
                      <Switch
                        checked={emailNotifications}
                        onCheckedChange={setEmailNotifications}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Task Assignments</Label>
                        <p className="text-sm text-gray-600">
                          Get notified when you're assigned to a task
                        </p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Due Date Reminders</Label>
                        <p className="text-sm text-gray-600">
                          Receive reminders for upcoming due dates
                        </p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>AI Suggestions</Label>
                        <p className="text-sm text-gray-600">
                          Get notified about AI-generated suggestions
                        </p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <Button onClick={handleUpdateProfile} disabled={loading}>
                      {loading ? 'Updating...' : 'Save Preferences'}
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Appearance Settings */}
              <TabsContent value="appearance">
                <Card>
                  <CardHeader>
                    <CardTitle>Appearance</CardTitle>
                    <CardDescription>
                      Customize the look and feel of your workspace.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label>Theme</Label>
                      <Select value={theme} onValueChange={(value: string) => setTheme(value as 'light' | 'dark' | 'system')}>
                        <SelectTrigger className="w-48">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                          <SelectItem value="system">System</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button onClick={handleUpdateProfile} disabled={loading}>
                      {loading ? 'Updating...' : 'Save Appearance'}
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* AI Settings */}
              <TabsContent value="ai">
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>AI Preferences</CardTitle>
                      <CardDescription>
                        Configure your AI assistant preferences and behavior.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Auto-approve simple suggestions</Label>
                          <p className="text-sm text-gray-600">
                            Automatically approve low-risk AI suggestions
                          </p>
                        </div>
                        <Switch />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Proactive suggestions</Label>
                          <p className="text-sm text-gray-600">
                            Allow AI to make suggestions without being asked
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                      <div className="space-y-2">
                        <Label>Default AI Model</Label>
                        <Select defaultValue="claude-3-sonnet">
                          <SelectTrigger className="w-64">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                            <SelectItem value="gpt-4">GPT-4</SelectItem>
                            <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>

                  {/* MCP Settings */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Server className="h-5 w-5 mr-2" />
                        MCP (Model Context Protocol)
                      </CardTitle>
                      <CardDescription>
                        Configure server connections for advanced AI capabilities.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-8">
                        <Server className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          No MCP connections
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Connect to servers to enable advanced AI capabilities like file access and command execution.
                        </p>
                        <Button>
                          <Plus className="h-4 w-4 mr-2" />
                          Add MCP Connection
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Admin Settings */}
              {isAdmin && (
                <TabsContent value="admin">
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center">
                          <Key className="h-5 w-5 mr-2" />
                          OpenRouter API Keys
                        </CardTitle>
                        <CardDescription>
                          Manage API keys for AI model access. Keys are encrypted and secure.
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {apiKeys.map((key) => (
                            <div key={key.id} className="flex items-center justify-between p-3 border rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div>
                                  <p className="font-medium">{key.name}</p>
                                  <p className="text-sm text-gray-600">{key.masked}</p>
                                </div>
                                <Badge variant={key.active ? 'default' : 'secondary'}>
                                  {key.active ? 'Active' : 'Inactive'}
                                </Badge>
                              </div>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                          <Button className="w-full" variant="outline">
                            <Plus className="h-4 w-4 mr-2" />
                            Add API Key
                          </Button>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Enabled AI Models</CardTitle>
                        <CardDescription>
                          Choose which AI models are available to users.
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {[
                              { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', provider: 'Anthropic' },
                              { id: 'claude-3-haiku', name: 'Claude 3 Haiku', provider: 'Anthropic' },
                              { id: 'gpt-4', name: 'GPT-4', provider: 'OpenAI' },
                              { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'OpenAI' },
                            ].map((model) => (
                              <div key={model.id} className="flex items-center justify-between p-3 border rounded-lg">
                                <div>
                                  <p className="font-medium">{model.name}</p>
                                  <p className="text-sm text-gray-600">{model.provider}</p>
                                </div>
                                <Switch
                                  checked={enabledModels.includes(model.id)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setEnabledModels([...enabledModels, model.id]);
                                    } else {
                                      setEnabledModels(enabledModels.filter(id => id !== model.id));
                                    }
                                  }}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              )}
            </Tabs>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}