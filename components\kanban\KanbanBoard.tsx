'use client';

import { useEffect, useState } from 'react';
import { DndContext, DragEndEvent, DragOverEvent, DragStartEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, horizontalListSortingStrategy } from '@dnd-kit/sortable';
import { useBoardStore } from '@/lib/store';
import { Board, BoardColumn } from '@/lib/supabase';
import { KanbanColumn } from './KanbanColumn';
import { TaskCard } from './TaskCard';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface KanbanBoardProps {
  board: Board;
}

export function KanbanBoard({ board }: KanbanBoardProps) {
  const [columns, setColumns] = useState<BoardColumn[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);
  const { tasks, fetchTasks, moveTask } = useBoardStore();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  useEffect(() => {
    fetchColumns();
    fetchTasks(board.id);
  }, [board.id, fetchTasks]);

  const fetchColumns = async () => {
    const { data, error } = await supabase
      .from('board_columns')
      .select('*')
      .eq('board_id', board.id)
      .order('position');
    
    if (error) {
      console.error('Error fetching columns:', error);
      return;
    }
    
    setColumns(data || []);
  };

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragOver = (event: DragOverEvent) => {
    // Handle drag over logic if needed
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (!over) return;
    
    const taskId = active.id as string;
    const overId = over.id as string;
    
    // Check if dropping on a column
    const targetColumn = columns.find(col => col.id === overId);
    if (targetColumn) {
      const tasksInColumn = tasks.filter(task => task.column_id === targetColumn.id);
      await moveTask(taskId, targetColumn.id, tasksInColumn.length);
    }
    
    setActiveId(null);
  };

  const getTasksByColumn = (columnId: string) => {
    return tasks.filter(task => task.column_id === columnId);
  };

  return (
    <div className="h-full flex flex-col">
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="flex-1 overflow-x-auto">
          <div className="flex space-x-6 h-full min-w-max p-1">
            <SortableContext items={columns.map(col => col.id)} strategy={horizontalListSortingStrategy}>
              {columns.map((column) => (
                <KanbanColumn
                  key={column.id}
                  column={column}
                  tasks={getTasksByColumn(column.id)}
                />
              ))}
            </SortableContext>
            
            {/* Add Column Button */}
            <div className="flex-shrink-0 w-80">
              <Button
                variant="outline"
                className="w-full h-12 border-dashed border-2 text-gray-500 hover:text-gray-700 hover:border-gray-400"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Column
              </Button>
            </div>
          </div>
        </div>
      </DndContext>
    </div>
  );
}