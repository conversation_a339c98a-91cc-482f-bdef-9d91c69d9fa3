# ProjectFlow - AI-Powered Project Management Platform

A modern, collaborative project management platform inspired by <PERSON><PERSON><PERSON> and <PERSON><PERSON>, featuring an intelligent AI assistant powered by OpenRouter API.

## 🚀 Features

### 📦 Project Management
- **Multiple Projects**: Create and manage unlimited projects
- **Kanban Boards**: Drag-and-drop task management with customizable columns
- **Rich Tasks**: HTML descriptions, priorities, due dates, tags, and dependencies
- **File Attachments**: Upload and manage files via Supabase Storage
- **Time Tracking**: Optional time logging for tasks
- **Subtasks**: Break down complex tasks into manageable pieces
- **Comments**: Threaded discussions on tasks

### 👥 Collaboration
- **Team Invitations**: Invite users via email to join projects
- **Role-Based Access**: Admin and Member roles with proper permissions
- **Real-time Updates**: Live collaboration with Supabase real-time subscriptions
- **Activity Logging**: Complete audit trail of all project activities

### 🤖 AI Assistant
- **Intelligent Chat**: Context-aware AI assistant that understands your projects
- **Task Analysis**: AI can read and analyze your tasks, users, and project data
- **Smart Suggestions**: AI suggests optimizations, deadlines, and task reorganizations
- **Automated Actions**: AI can create tasks, assign users, and set priorities
- **Approval Workflow**: All AI changes require explicit user approval
- **Multiple Models**: Support for Claude, GPT-4, and other OpenRouter models

### 🔐 Security & Admin
- **Row Level Security**: Supabase RLS ensures users only see their data
- **Admin Panel**: Manage OpenRouter API keys and enabled models
- **Encrypted Storage**: API keys and sensitive data are encrypted
- **Audit Logging**: Complete activity logs for compliance

### 🛠️ Optional Features
- **MCP Integration**: Connect to servers for advanced AI capabilities
- **Project Templates**: Quick project setup with predefined structures
- **Custom Themes**: Light/dark mode and appearance customization

## 🧰 Tech Stack

- **Frontend**: Next.js 13+ with App Router
- **Styling**: Tailwind CSS + shadcn/ui components
- **Icons**: Heroicons React
- **State Management**: Zustand
- **Backend**: Supabase (PostgreSQL + Auth + Storage + Real-time)
- **AI Integration**: OpenRouter API
- **Rich Text**: TipTap HTML editor
- **Drag & Drop**: @dnd-kit
- **Type Safety**: TypeScript throughout

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Supabase account
- OpenRouter API account (for AI features)

### 1. Clone and Install
```bash
git clone <repository-url>
cd projectflow
npm install
```

### 2. Environment Setup
```bash
cp .env.local.example .env.local
```

Fill in your environment variables:
- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anon key
- `OPENROUTER_API_KEY`: Your OpenRouter API key

### 3. Database Setup
1. Create a new Supabase project
2. Run the migration file in your Supabase SQL editor:
   ```sql
   -- Copy and paste the contents of supabase/migrations/enhanced_project_management_schema.sql
   ```
3. Enable Row Level Security (RLS) is automatically configured

### 4. Storage Setup (Optional)
If you want file attachments:
1. Go to Supabase Storage
2. Create a bucket named `task-attachments`
3. Set appropriate policies for authenticated users

### 5. Run Development Server
```bash
npm run dev
```

Visit `http://localhost:3000` to see your application.

## 📁 Project Structure

```
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Main dashboard
│   ├── project/[id]/      # Project-specific pages
│   ├── ai/                # AI Assistant interface
│   └── settings/          # User settings
├── components/            # Reusable UI components
│   ├── ui/               # shadcn/ui components
│   ├── layout/           # Layout components
│   └── kanban/           # Kanban board components
├── lib/                  # Utilities and configurations
│   ├── supabase.ts       # Supabase client and types
│   ├── store.ts          # Zustand state management
│   └── utils.ts          # Utility functions
└── supabase/
    └── migrations/       # Database schema
```

## 🔧 Configuration

### AI Models
The platform supports multiple AI models through OpenRouter:
- Claude 3 Sonnet (balanced performance)
- Claude 3 Haiku (fast responses)
- GPT-4 (advanced reasoning)
- GPT-3.5 Turbo (cost-effective)

Admins can enable/disable models and set custom display names.

### Database Schema
The database includes comprehensive tables for:
- User profiles and authentication
- Projects and team management
- Kanban boards with custom columns
- Tasks with rich metadata
- AI conversations and suggestions
- Activity logging and audit trails

### Security
- **Row Level Security (RLS)**: All tables have proper RLS policies
- **API Key Encryption**: Sensitive data is encrypted at rest
- **User Permissions**: Role-based access control throughout
- **Audit Logging**: Complete activity tracking

## 🤖 AI Features

### Chat Interface
- Context-aware conversations about your projects
- Support for multiple AI models
- Conversation history and management
- Real-time responses

### Smart Suggestions
- Task creation and optimization
- Priority and deadline suggestions
- Team member assignments
- Project health analysis

### Approval Workflow
- All AI actions require user approval
- Clear preview of proposed changes
- Batch approval for multiple suggestions
- Audit trail of AI interactions

## 🔌 MCP Integration (Optional)

Model Context Protocol allows AI to:
- Access server files and directories
- Execute terminal commands
- Interact with development tools
- Perform automated deployments

**Security**: All MCP actions require explicit user approval and are logged.

## 📊 Admin Features

### OpenRouter Management
- Add/remove API keys
- Enable/disable AI models
- Set custom model display names
- Monitor API usage

### User Management
- View all users and their roles
- Promote users to admin
- Monitor project activity
- System-wide settings

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push

### Other Platforms
The app can be deployed to any platform supporting Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Join our community discussions

---

Built with ❤️ using Next.js, Supabase, and OpenRouter AI.