/*
  # Enhanced Project Management Platform Database Schema

  1. New Tables
    - `profiles` - Extended user profiles with roles and preferences
    - `projects` - Main project containers with templates support
    - `project_templates` - Reusable project templates
    - `project_members` - Project membership with roles (<PERSON><PERSON>, Member)
    - `boards` - Kanban boards within projects  
    - `board_columns` - Custom columns for each board (To Do, In Progress, Done, etc.)
    - `tasks` - Main tasks with rich content, dependencies, and file attachments
    - `task_dependencies` - Task dependency relationships
    - `task_time_logs` - Optional time tracking
    - `task_attachments` - File attachments via Supabase Storage
    - `subtasks` - Sub-tasks belonging to main tasks
    - `task_comments` - Comments on tasks with threading support
    - `task_assignments` - Many-to-many relationship for task assignees
    - `invitations` - Project invitation system
    - `activity_logs` - Audit trail for all actions
    - `ai_conversations` - Chat history with AI agent
    - `ai_suggestions` - AI-generated suggestions awaiting approval
    - `openrouter_settings` - Admin settings for AI integration
    - `mcp_credentials` - Encrypted SSH credentials for MCP (optional)

  2. Security
    - Enable RLS on all tables
    - Comprehensive policies for user/project-specific access
    - Admin-only access for sensitive settings

  3. Features
    - Support for custom board columns with positioning
    - Rich task metadata (priority, due dates, tags, dependencies)
    - File attachments via Supabase Storage
    - Optional time tracking
    - Project templates for quick setup
    - Collaborative features with proper permissions
    - AI integration foundation
    - Audit logging
*/

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('admin', 'user');
CREATE TYPE project_role AS ENUM ('admin', 'member');
CREATE TYPE task_priority AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE invitation_status AS ENUM ('pending', 'accepted', 'declined');
CREATE TYPE ai_suggestion_status AS ENUM ('pending', 'approved', 'rejected');
CREATE TYPE dependency_type AS ENUM ('blocks', 'blocked_by');

-- Profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  full_name text NOT NULL,
  avatar_url text,
  role user_role DEFAULT 'user',
  timezone text DEFAULT 'UTC',
  preferences jsonb DEFAULT '{"theme": "light", "notifications": true}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Project templates for quick setup
CREATE TABLE IF NOT EXISTS project_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text DEFAULT '',
  template_data jsonb NOT NULL, -- Contains boards, columns, and sample tasks
  is_public boolean DEFAULT false,
  created_by uuid NOT NULL REFERENCES profiles(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Projects table
CREATE TABLE IF NOT EXISTS projects (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text DEFAULT '',
  owner_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  template_id uuid REFERENCES project_templates(id),
  settings jsonb DEFAULT '{"ai_enabled": true, "mcp_enabled": false, "time_tracking": false}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Project members with roles
CREATE TABLE IF NOT EXISTS project_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id uuid NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  role project_role DEFAULT 'member',
  joined_at timestamptz DEFAULT now(),
  UNIQUE(project_id, user_id)
);

-- Boards within projects
CREATE TABLE IF NOT EXISTS boards (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id uuid NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text DEFAULT '',
  position integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Board columns (customizable with positioning)
CREATE TABLE IF NOT EXISTS board_columns (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  board_id uuid NOT NULL REFERENCES boards(id) ON DELETE CASCADE,
  name text NOT NULL,
  position integer DEFAULT 0,
  color text DEFAULT '#6b7280',
  wip_limit integer, -- Work in progress limit
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Main tasks table with dependencies and attachments
CREATE TABLE IF NOT EXISTS tasks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  board_id uuid NOT NULL REFERENCES boards(id) ON DELETE CASCADE,
  column_id uuid NOT NULL REFERENCES board_columns(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text DEFAULT '', -- Rich HTML content from TipTap
  priority task_priority DEFAULT 'medium',
  due_date timestamptz,
  estimated_hours numeric(5,2),
  actual_hours numeric(5,2) DEFAULT 0,
  tags text[] DEFAULT '{}',
  position integer DEFAULT 0,
  created_by uuid NOT NULL REFERENCES profiles(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Task dependencies
CREATE TABLE IF NOT EXISTS task_dependencies (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id uuid NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  depends_on_task_id uuid NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  dependency_type dependency_type DEFAULT 'blocks',
  created_at timestamptz DEFAULT now(),
  UNIQUE(task_id, depends_on_task_id),
  CHECK (task_id != depends_on_task_id) -- Prevent self-dependencies
);

-- Task attachments via Supabase Storage
CREATE TABLE IF NOT EXISTS task_attachments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id uuid NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  file_name text NOT NULL,
  file_path text NOT NULL, -- Path in Supabase Storage
  file_size bigint NOT NULL,
  mime_type text NOT NULL,
  uploaded_by uuid NOT NULL REFERENCES profiles(id),
  uploaded_at timestamptz DEFAULT now()
);

-- Time tracking logs
CREATE TABLE IF NOT EXISTS task_time_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id uuid NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  hours numeric(5,2) NOT NULL,
  description text DEFAULT '',
  logged_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Subtasks
CREATE TABLE IF NOT EXISTS subtasks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id uuid NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text DEFAULT '',
  completed boolean DEFAULT false,
  position integer DEFAULT 0,
  created_by uuid NOT NULL REFERENCES profiles(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Task assignments (many-to-many)
CREATE TABLE IF NOT EXISTS task_assignments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id uuid NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  assigned_by uuid NOT NULL REFERENCES profiles(id),
  assigned_at timestamptz DEFAULT now(),
  UNIQUE(task_id, user_id)
);

-- Task comments with threading
CREATE TABLE IF NOT EXISTS task_comments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id uuid NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  parent_id uuid REFERENCES task_comments(id) ON DELETE CASCADE,
  content text NOT NULL,
  created_by uuid NOT NULL REFERENCES profiles(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Project invitations
CREATE TABLE IF NOT EXISTS invitations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id uuid NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  email text NOT NULL,
  role project_role DEFAULT 'member',
  invited_by uuid NOT NULL REFERENCES profiles(id),
  status invitation_status DEFAULT 'pending',
  token text UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
  expires_at timestamptz DEFAULT (now() + interval '7 days'),
  created_at timestamptz DEFAULT now()
);

-- Activity logging
CREATE TABLE IF NOT EXISTS activity_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id uuid REFERENCES projects(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES profiles(id),
  action text NOT NULL,
  entity_type text, -- 'task', 'board', 'project', etc.
  entity_id uuid,
  details jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

-- AI conversation history
CREATE TABLE IF NOT EXISTS ai_conversations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  project_id uuid REFERENCES projects(id) ON DELETE CASCADE,
  title text DEFAULT 'New Conversation',
  messages jsonb NOT NULL DEFAULT '[]',
  model_name text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- AI suggestions awaiting approval
CREATE TABLE IF NOT EXISTS ai_suggestions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  project_id uuid REFERENCES projects(id) ON DELETE CASCADE,
  conversation_id uuid REFERENCES ai_conversations(id) ON DELETE CASCADE,
  suggestion_type text NOT NULL, -- 'create_task', 'update_task', 'assign_task', etc.
  suggestion_data jsonb NOT NULL,
  status ai_suggestion_status DEFAULT 'pending',
  approved_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Admin settings for OpenRouter API
CREATE TABLE IF NOT EXISTS openrouter_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  api_keys jsonb DEFAULT '[]', -- Array of encrypted API keys
  enabled_models jsonb DEFAULT '[]', -- Array of model IDs
  model_display_names jsonb DEFAULT '{}', -- Object mapping model IDs to display names
  created_by uuid NOT NULL REFERENCES profiles(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- MCP credentials (encrypted)
CREATE TABLE IF NOT EXISTS mcp_credentials (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  project_id uuid REFERENCES projects(id) ON DELETE CASCADE,
  name text NOT NULL,
  host text NOT NULL,
  username text NOT NULL,
  encrypted_password text NOT NULL,
  port integer DEFAULT 22,
  enabled boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE boards ENABLE ROW LEVEL SECURITY;
ALTER TABLE board_columns ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_time_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE subtasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE openrouter_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE mcp_credentials ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Profiles: Users can read all profiles but only update their own
CREATE POLICY "Profiles are viewable by authenticated users" ON profiles
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE TO authenticated USING (auth.uid() = id);

-- Project templates: Public templates viewable by all, private by creator
CREATE POLICY "Public templates are viewable by all" ON project_templates
  FOR SELECT TO authenticated USING (is_public = true OR created_by = auth.uid());

CREATE POLICY "Users can create templates" ON project_templates
  FOR INSERT TO authenticated WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can update own templates" ON project_templates
  FOR UPDATE TO authenticated USING (created_by = auth.uid());

-- Projects: Users can see projects they're members of
CREATE POLICY "Users can view projects they're members of" ON projects
  FOR SELECT TO authenticated USING (
    id IN (
      SELECT project_id FROM project_members WHERE user_id = auth.uid()
    ) OR owner_id = auth.uid()
  );

CREATE POLICY "Users can create projects" ON projects
  FOR INSERT TO authenticated WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Project owners and admins can update projects" ON projects
  FOR UPDATE TO authenticated USING (
    owner_id = auth.uid() OR 
    id IN (
      SELECT project_id FROM project_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Project members: Users can see members of projects they belong to
CREATE POLICY "Users can view project members" ON project_members
  FOR SELECT TO authenticated USING (
    project_id IN (
      SELECT project_id FROM project_members WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Project admins can manage members" ON project_members
  FOR ALL TO authenticated USING (
    project_id IN (
      SELECT p.id FROM projects p 
      WHERE p.owner_id = auth.uid()
      UNION
      SELECT pm.project_id FROM project_members pm
      WHERE pm.user_id = auth.uid() AND pm.role = 'admin'
    )
  );

-- Boards: Users can see boards in projects they're members of
CREATE POLICY "Users can view project boards" ON boards
  FOR SELECT TO authenticated USING (
    project_id IN (
      SELECT project_id FROM project_members WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Project members can manage boards" ON boards
  FOR ALL TO authenticated USING (
    project_id IN (
      SELECT project_id FROM project_members WHERE user_id = auth.uid()
    )
  );

-- Board columns: Same as boards
CREATE POLICY "Users can view board columns" ON board_columns
  FOR SELECT TO authenticated USING (
    board_id IN (
      SELECT b.id FROM boards b
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

CREATE POLICY "Project members can manage board columns" ON board_columns
  FOR ALL TO authenticated USING (
    board_id IN (
      SELECT b.id FROM boards b
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

-- Tasks: Users can see tasks in projects they're members of
CREATE POLICY "Users can view project tasks" ON tasks
  FOR SELECT TO authenticated USING (
    board_id IN (
      SELECT b.id FROM boards b
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

CREATE POLICY "Project members can manage tasks" ON tasks
  FOR ALL TO authenticated USING (
    board_id IN (
      SELECT b.id FROM boards b
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

-- Task dependencies: Same access as tasks
CREATE POLICY "Users can view task dependencies" ON task_dependencies
  FOR SELECT TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

CREATE POLICY "Project members can manage task dependencies" ON task_dependencies
  FOR ALL TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

-- Task attachments: Same access as tasks
CREATE POLICY "Users can view task attachments" ON task_attachments
  FOR SELECT TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

CREATE POLICY "Project members can manage task attachments" ON task_attachments
  FOR ALL TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

-- Time logs: Same access as tasks
CREATE POLICY "Users can view task time logs" ON task_time_logs
  FOR SELECT TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

CREATE POLICY "Project members can manage time logs" ON task_time_logs
  FOR ALL TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

-- Subtasks: Same access as parent tasks
CREATE POLICY "Users can view subtasks" ON subtasks
  FOR SELECT TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

CREATE POLICY "Project members can manage subtasks" ON subtasks
  FOR ALL TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

-- Task assignments: Same access as tasks
CREATE POLICY "Users can view task assignments" ON task_assignments
  FOR SELECT TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

CREATE POLICY "Project members can manage task assignments" ON task_assignments
  FOR ALL TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

-- Task comments: Same access as tasks
CREATE POLICY "Users can view task comments" ON task_comments
  FOR SELECT TO authenticated USING (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    )
  );

CREATE POLICY "Project members can create comments" ON task_comments
  FOR INSERT TO authenticated WITH CHECK (
    task_id IN (
      SELECT t.id FROM tasks t
      JOIN boards b ON t.board_id = b.id
      JOIN project_members pm ON b.project_id = pm.project_id
      WHERE pm.user_id = auth.uid()
    ) AND created_by = auth.uid()
  );

CREATE POLICY "Users can update own comments" ON task_comments
  FOR UPDATE TO authenticated USING (created_by = auth.uid());

-- Invitations: Only admins can manage
CREATE POLICY "Users can view invitations they sent or received" ON invitations
  FOR SELECT TO authenticated USING (
    invited_by = auth.uid() OR 
    email = (SELECT email FROM profiles WHERE id = auth.uid())
  );

CREATE POLICY "Project admins can manage invitations" ON invitations
  FOR ALL TO authenticated USING (
    project_id IN (
      SELECT p.id FROM projects p 
      WHERE p.owner_id = auth.uid()
      UNION
      SELECT pm.project_id FROM project_members pm
      WHERE pm.user_id = auth.uid() AND pm.role = 'admin'
    )
  );

-- Activity logs: Read-only for project members
CREATE POLICY "Users can view project activity logs" ON activity_logs
  FOR SELECT TO authenticated USING (
    project_id IN (
      SELECT project_id FROM project_members WHERE user_id = auth.uid()
    )
  );

-- AI conversations: Users can only see their own
CREATE POLICY "Users can manage own AI conversations" ON ai_conversations
  FOR ALL TO authenticated USING (user_id = auth.uid());

-- AI suggestions: Users can only see their own
CREATE POLICY "Users can manage own AI suggestions" ON ai_suggestions
  FOR ALL TO authenticated USING (user_id = auth.uid());

-- OpenRouter settings: Admin only
CREATE POLICY "Only admins can manage OpenRouter settings" ON openrouter_settings
  FOR ALL TO authenticated USING (
    auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin')
  );

-- MCP credentials: Users can only see their own
CREATE POLICY "Users can manage own MCP credentials" ON mcp_credentials
  FOR ALL TO authenticated USING (user_id = auth.uid());

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_owner ON projects(owner_id);
CREATE INDEX IF NOT EXISTS idx_project_members_user ON project_members(user_id);
CREATE INDEX IF NOT EXISTS idx_project_members_project ON project_members(project_id);
CREATE INDEX IF NOT EXISTS idx_boards_project ON boards(project_id);
CREATE INDEX IF NOT EXISTS idx_board_columns_board ON board_columns(board_id);
CREATE INDEX IF NOT EXISTS idx_board_columns_position ON board_columns(board_id, position);
CREATE INDEX IF NOT EXISTS idx_tasks_board ON tasks(board_id);
CREATE INDEX IF NOT EXISTS idx_tasks_column ON tasks(column_id);
CREATE INDEX IF NOT EXISTS idx_tasks_position ON tasks(column_id, position);
CREATE INDEX IF NOT EXISTS idx_task_dependencies_task ON task_dependencies(task_id);
CREATE INDEX IF NOT EXISTS idx_task_dependencies_depends ON task_dependencies(depends_on_task_id);
CREATE INDEX IF NOT EXISTS idx_task_attachments_task ON task_attachments(task_id);
CREATE INDEX IF NOT EXISTS idx_task_time_logs_task ON task_time_logs(task_id);
CREATE INDEX IF NOT EXISTS idx_task_time_logs_user ON task_time_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_subtasks_task ON subtasks(task_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_task ON task_assignments(task_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_user ON task_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_task_comments_task ON task_comments(task_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_project ON activity_logs(project_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_conversations_user ON ai_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_suggestions_user ON ai_suggestions(user_id);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_templates_updated_at BEFORE UPDATE ON project_templates 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_boards_updated_at BEFORE UPDATE ON boards 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_board_columns_updated_at BEFORE UPDATE ON board_columns 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subtasks_updated_at BEFORE UPDATE ON subtasks 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_comments_updated_at BEFORE UPDATE ON task_comments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_conversations_updated_at BEFORE UPDATE ON ai_conversations 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create default board columns function
CREATE OR REPLACE FUNCTION create_default_board_columns(board_id_param uuid)
RETURNS void AS $$
BEGIN
  INSERT INTO board_columns (board_id, name, position, color) VALUES
    (board_id_param, 'To Do', 0, '#ef4444'),
    (board_id_param, 'In Progress', 1, '#f59e0b'),
    (board_id_param, 'Done', 2, '#10b981');
END;
$$ language 'plpgsql';

-- Function to log activities
CREATE OR REPLACE FUNCTION log_activity(
  p_project_id uuid,
  p_user_id uuid,
  p_action text,
  p_entity_type text DEFAULT NULL,
  p_entity_id uuid DEFAULT NULL,
  p_details jsonb DEFAULT '{}'
)
RETURNS void AS $$
BEGIN
  INSERT INTO activity_logs (project_id, user_id, action, entity_type, entity_id, details)
  VALUES (p_project_id, p_user_id, p_action, p_entity_type, p_entity_id, p_details);
END;
$$ language 'plpgsql';