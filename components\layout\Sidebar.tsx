'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuthStore, useProjectStore } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  BrainCircuit, 
  FolderOpen, 
  MessageSquare, 
  Settings, 
  LogOut,
  ChevronLeft,
  ChevronRight,
  Plus
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const [collapsed, setCollapsed] = useState(false);
  const pathname = usePathname();
  const { profile, signOut } = useAuthStore();
  const { projects, currentProject, setCurrentProject } = useProjectStore();

  const navigation = [
    {
      name: 'Projects',
      href: '/dashboard',
      icon: FolderOpen,
      current: pathname === '/dashboard',
    },
    {
      name: 'AI Assistant',
      href: '/ai',
      icon: MessageSquare,
      current: pathname.startsWith('/ai'),
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      current: pathname.startsWith('/settings'),
    },
  ];

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div className={cn(
      'flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300',
      collapsed ? 'w-16' : 'w-64',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!collapsed && (
          <div className="flex items-center">
            <BrainCircuit className="h-8 w-8 text-blue-600 mr-2" />
            <span className="text-xl font-bold text-gray-900">ProjectFlow</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCollapsed(!collapsed)}
          className="p-1"
        >
          {collapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigation.map((item) => (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
              item.current
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            )}
          >
            <item.icon className={cn('h-5 w-5', collapsed ? '' : 'mr-3')} />
            {!collapsed && item.name}
          </Link>
        ))}

        {/* Projects Section */}
        {!collapsed && (
          <div className="mt-8">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Projects
              </h3>
              <Button variant="ghost" size="sm" className="p-1">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="space-y-1">
              {projects.slice(0, 5).map((project) => (
                <button
                  key={project.id}
                  onClick={() => setCurrentProject(project)}
                  className={cn(
                    'w-full text-left px-3 py-2 text-sm rounded-md transition-colors',
                    currentProject?.id === project.id
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50'
                  )}
                >
                  <div className="truncate">{project.name}</div>
                </button>
              ))}
            </div>
          </div>
        )}
      </nav>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-200">
        {!collapsed && profile && (
          <div className="flex items-center mb-3">
            <Avatar className="h-8 w-8 mr-3">
              <AvatarImage src={profile.avatar_url} />
              <AvatarFallback>
                {profile.full_name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {profile.full_name}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {profile.email}
              </p>
            </div>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSignOut}
          className={cn(
            'w-full justify-start text-gray-600 hover:text-gray-900',
            collapsed && 'justify-center'
          )}
        >
          <LogOut className={cn('h-4 w-4', collapsed ? '' : 'mr-2')} />
          {!collapsed && 'Sign Out'}
        </Button>
      </div>
    </div>
  );
}