'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { KanbanBoard } from '@/components/kanban/KanbanBoard';
import { useProjectStore, useBoardStore } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Settings, Users } from 'lucide-react';

export default function ProjectPage() {
  const params = useParams();
  const projectId = params.id as string;
  
  const { projects, currentProject, setCurrentProject } = useProjectStore();
  const { boards, fetchBoards, createBoard } = useBoardStore();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const project = projects.find(p => p.id === projectId);
    if (project) {
      setCurrentProject(project);
      fetchBoards(projectId);
    }
  }, [projectId, projects, setCurrentProject, fetchBoards]);

  const handleCreateBoard = async () => {
    if (!currentProject) return;
    
    setLoading(true);
    try {
      await createBoard(currentProject.id, 'New Board', 'A new Kanban board');
    } catch (error) {
      console.error('Error creating board:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!currentProject) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Project not found</h2>
            <p className="text-gray-600">The project you're looking for doesn't exist or you don't have access to it.</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{currentProject.name}</h1>
              <p className="text-gray-600">{currentProject.description}</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Users className="h-4 w-4 mr-2" />
                Members
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="boards" className="h-full flex flex-col">
            <div className="border-b border-gray-200 px-6">
              <TabsList className="grid w-full max-w-md grid-cols-3">
                <TabsTrigger value="boards">Boards</TabsTrigger>
                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                <TabsTrigger value="reports">Reports</TabsTrigger>
              </TabsList>
            </div>
            
            <TabsContent value="boards" className="flex-1 overflow-hidden m-0">
              {boards.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No boards yet</h3>
                    <p className="text-gray-600 mb-6">Create your first Kanban board to get started</p>
                    <Button onClick={handleCreateBoard} disabled={loading}>
                      <Plus className="h-4 w-4 mr-2" />
                      {loading ? 'Creating...' : 'Create Board'}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="h-full">
                  <div className="flex items-center justify-between p-6 pb-4">
                    <h2 className="text-lg font-semibold text-gray-900">
                      {boards[0]?.name || 'Board'}
                    </h2>
                    <Button onClick={handleCreateBoard} disabled={loading} size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Board
                    </Button>
                  </div>
                  <div className="flex-1 overflow-hidden px-6">
                    <KanbanBoard board={boards[0]} />
                  </div>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="timeline" className="flex-1 p-6">
              <div className="text-center py-12">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Timeline View</h3>
                <p className="text-gray-600">Timeline view coming soon...</p>
              </div>
            </TabsContent>
            
            <TabsContent value="reports" className="flex-1 p-6">
              <div className="text-center py-12">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Reports</h3>
                <p className="text-gray-600">Project reports coming soon...</p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </DashboardLayout>
  );
}