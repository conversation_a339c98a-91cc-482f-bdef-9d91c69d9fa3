import { create } from 'zustand';
import { supabase, Profile, Project, Board, Task, AIConversation } from './supabase';

interface AuthState {
  user: any;
  profile: Profile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
}

interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  loading: boolean;
  fetchProjects: () => Promise<void>;
  createProject: (name: string, description?: string) => Promise<Project>;
  updateProject: (id: string, updates: Partial<Project>) => Promise<void>;
  deleteProject: (id: string) => Promise<void>;
  setCurrentProject: (project: Project | null) => void;
}

interface BoardState {
  boards: Board[];
  currentBoard: Board | null;
  tasks: Task[];
  loading: boolean;
  fetchBoards: (projectId: string) => Promise<void>;
  fetchTasks: (boardId: string) => Promise<void>;
  createBoard: (projectId: string, name: string, description?: string) => Promise<Board>;
  updateBoard: (id: string, updates: Partial<Board>) => Promise<void>;
  deleteBoard: (id: string) => Promise<void>;
  setCurrentBoard: (board: Board | null) => void;
  createTask: (boardId: string, columnId: string, title: string, description?: string) => Promise<Task>;
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  moveTask: (taskId: string, columnId: string, position: number) => Promise<void>;
}

interface AIState {
  conversations: AIConversation[];
  currentConversation: AIConversation | null;
  loading: boolean;
  fetchConversations: () => Promise<void>;
  createConversation: (projectId?: string, title?: string) => Promise<AIConversation>;
  updateConversation: (id: string, updates: Partial<AIConversation>) => Promise<void>;
  deleteConversation: (id: string) => Promise<void>;
  setCurrentConversation: (conversation: AIConversation | null) => void;
  sendMessage: (conversationId: string, message: string, model: string) => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  profile: null,
  loading: true,

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) throw error;
    
    // Fetch profile
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', data.user.id)
      .single();
    
    set({ user: data.user, profile });
  },

  signUp: async (email: string, password: string, fullName: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });
    if (error) throw error;

    if (data.user) {
      // Create profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: data.user.id,
          email,
          full_name: fullName,
        });
      if (profileError) throw profileError;
    }
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    set({ user: null, profile: null });
  },

  updateProfile: async (updates: Partial<Profile>) => {
    const { profile } = get();
    if (!profile) return;

    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', profile.id)
      .select()
      .single();
    
    if (error) throw error;
    set({ profile: data });
  },
}));

export const useProjectStore = create<ProjectState>((set, get) => ({
  projects: [],
  currentProject: null,
  loading: false,

  fetchProjects: async () => {
    set({ loading: true });
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        project_members!inner(role),
        profiles!projects_owner_id_fkey(full_name, avatar_url)
      `)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    set({ projects: data || [], loading: false });
  },

  createProject: async (name: string, description = '') => {
    const { data, error } = await supabase
      .from('projects')
      .insert({
        name,
        description,
        owner_id: (await supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();
    
    if (error) throw error;

    // Add creator as admin member
    await supabase
      .from('project_members')
      .insert({
        project_id: data.id,
        user_id: data.owner_id,
        role: 'admin',
      });

    // Refresh projects
    get().fetchProjects();
    return data;
  },

  updateProject: async (id: string, updates: Partial<Project>) => {
    const { error } = await supabase
      .from('projects')
      .update(updates)
      .eq('id', id);
    
    if (error) throw error;
    get().fetchProjects();
  },

  deleteProject: async (id: string) => {
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
    get().fetchProjects();
  },

  setCurrentProject: (project: Project | null) => {
    set({ currentProject: project });
  },
}));

export const useBoardStore = create<BoardState>((set, get) => ({
  boards: [],
  currentBoard: null,
  tasks: [],
  loading: false,

  fetchBoards: async (projectId: string) => {
    set({ loading: true });
    const { data, error } = await supabase
      .from('boards')
      .select('*')
      .eq('project_id', projectId)
      .order('position');
    
    if (error) throw error;
    set({ boards: data || [], loading: false });
  },

  fetchTasks: async (boardId: string) => {
    set({ loading: true });
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        task_assignments(*, profiles(*)),
        subtasks(*),
        task_dependencies(*),
        task_attachments(*)
      `)
      .eq('board_id', boardId)
      .order('position');
    
    if (error) throw error;
    set({ tasks: data || [], loading: false });
  },

  createBoard: async (projectId: string, name: string, description = '') => {
    const { data, error } = await supabase
      .from('boards')
      .insert({
        project_id: projectId,
        name,
        description,
        position: get().boards.length,
      })
      .select()
      .single();
    
    if (error) throw error;

    // Create default columns
    await supabase.rpc('create_default_board_columns', {
      board_id_param: data.id,
    });

    get().fetchBoards(projectId);
    return data;
  },

  updateBoard: async (id: string, updates: Partial<Board>) => {
    const { error } = await supabase
      .from('boards')
      .update(updates)
      .eq('id', id);
    
    if (error) throw error;
    
    const { currentBoard } = get();
    if (currentBoard) {
      get().fetchBoards(currentBoard.project_id);
    }
  },

  deleteBoard: async (id: string) => {
    const { error } = await supabase
      .from('boards')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
    
    const { currentBoard } = get();
    if (currentBoard) {
      get().fetchBoards(currentBoard.project_id);
    }
  },

  setCurrentBoard: (board: Board | null) => {
    set({ currentBoard: board });
    if (board) {
      get().fetchTasks(board.id);
    }
  },

  createTask: async (boardId: string, columnId: string, title: string, description = '') => {
    const { data, error } = await supabase
      .from('tasks')
      .insert({
        board_id: boardId,
        column_id: columnId,
        title,
        description,
        created_by: (await supabase.auth.getUser()).data.user?.id,
        position: get().tasks.filter(t => t.column_id === columnId).length,
      })
      .select()
      .single();
    
    if (error) throw error;
    get().fetchTasks(boardId);
    return data;
  },

  updateTask: async (id: string, updates: Partial<Task>) => {
    const { error } = await supabase
      .from('tasks')
      .update(updates)
      .eq('id', id);
    
    if (error) throw error;
    
    const { currentBoard } = get();
    if (currentBoard) {
      get().fetchTasks(currentBoard.id);
    }
  },

  deleteTask: async (id: string) => {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
    
    const { currentBoard } = get();
    if (currentBoard) {
      get().fetchTasks(currentBoard.id);
    }
  },

  moveTask: async (taskId: string, columnId: string, position: number) => {
    const { error } = await supabase
      .from('tasks')
      .update({
        column_id: columnId,
        position,
      })
      .eq('id', taskId);
    
    if (error) throw error;
    
    const { currentBoard } = get();
    if (currentBoard) {
      get().fetchTasks(currentBoard.id);
    }
  },
}));

export const useAIStore = create<AIState>((set, get) => ({
  conversations: [],
  currentConversation: null,
  loading: false,

  fetchConversations: async () => {
    set({ loading: true });
    const { data, error } = await supabase
      .from('ai_conversations')
      .select('*')
      .order('updated_at', { ascending: false });
    
    if (error) throw error;
    set({ conversations: data || [], loading: false });
  },

  createConversation: async (projectId?: string, title = 'New Conversation') => {
    const { data, error } = await supabase
      .from('ai_conversations')
      .insert({
        user_id: (await supabase.auth.getUser()).data.user?.id,
        project_id: projectId,
        title,
        messages: [],
      })
      .select()
      .single();
    
    if (error) throw error;
    get().fetchConversations();
    return data;
  },

  updateConversation: async (id: string, updates: Partial<AIConversation>) => {
    const { error } = await supabase
      .from('ai_conversations')
      .update(updates)
      .eq('id', id);
    
    if (error) throw error;
    get().fetchConversations();
  },

  deleteConversation: async (id: string) => {
    const { error } = await supabase
      .from('ai_conversations')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
    get().fetchConversations();
  },

  setCurrentConversation: (conversation: AIConversation | null) => {
    set({ currentConversation: conversation });
  },

  sendMessage: async (conversationId: string, message: string, model: string) => {
    const { currentConversation } = get();
    if (!currentConversation) return;

    // Add user message
    const userMessage = {
      role: 'user' as const,
      content: message,
      timestamp: new Date().toISOString(),
    };

    const updatedMessages = [...currentConversation.messages, userMessage];

    // Update conversation with user message
    await get().updateConversation(conversationId, {
      messages: updatedMessages,
      model_name: model,
    });

    // TODO: Implement OpenRouter API call here
    // For now, add a placeholder assistant response
    const assistantMessage = {
      role: 'assistant' as const,
      content: 'This is a placeholder response. OpenRouter integration will be implemented next.',
      timestamp: new Date().toISOString(),
    };

    await get().updateConversation(conversationId, {
      messages: [...updatedMessages, assistantMessage],
    });
  },
}));

// Initialize auth state
supabase.auth.onAuthStateChange(async (event, session) => {
  if (session?.user) {
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.user.id)
      .single();

    useAuthStore.setState({ user: session.user, profile, loading: false });
  } else {
    useAuthStore.setState({ user: null, profile: null, loading: false });
  }
});