import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Profile {
  id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  role: 'admin' | 'user';
  timezone: string;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    notifications: boolean;
  };
  created_at: string;
  updated_at: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  owner_id: string;
  template_id?: string;
  settings: {
    ai_enabled: boolean;
    mcp_enabled: boolean;
    time_tracking: boolean;
  };
  created_at: string;
  updated_at: string;
}

export interface ProjectMember {
  id: string;
  project_id: string;
  user_id: string;
  role: 'admin' | 'member';
  joined_at: string;
  profile?: Profile;
}

export interface Board {
  id: string;
  project_id: string;
  name: string;
  description: string;
  position: number;
  created_at: string;
  updated_at: string;
}

export interface BoardColumn {
  id: string;
  board_id: string;
  name: string;
  position: number;
  color: string;
  wip_limit?: number;
  created_at: string;
  updated_at: string;
}

export interface Task {
  id: string;
  board_id: string;
  column_id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  due_date?: string;
  estimated_hours?: number;
  actual_hours: number;
  tags: string[];
  position: number;
  created_by: string;
  created_at: string;
  updated_at: string;
  assignments?: TaskAssignment[];
  subtasks?: Subtask[];
  dependencies?: TaskDependency[];
  attachments?: TaskAttachment[];
}

export interface TaskDependency {
  id: string;
  task_id: string;
  depends_on_task_id: string;
  dependency_type: 'blocks' | 'blocked_by';
  created_at: string;
}

export interface TaskAttachment {
  id: string;
  task_id: string;
  file_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  uploaded_by: string;
  uploaded_at: string;
}

export interface TaskTimeLog {
  id: string;
  task_id: string;
  user_id: string;
  hours: number;
  description: string;
  logged_at: string;
  created_at: string;
}

export interface Subtask {
  id: string;
  task_id: string;
  title: string;
  description: string;
  completed: boolean;
  position: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface TaskAssignment {
  id: string;
  task_id: string;
  user_id: string;
  assigned_by: string;
  assigned_at: string;
  profile?: Profile;
}

export interface TaskComment {
  id: string;
  task_id: string;
  parent_id?: string;
  content: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  profile?: Profile;
  replies?: TaskComment[];
}

export interface Invitation {
  id: string;
  project_id: string;
  email: string;
  role: 'admin' | 'member';
  invited_by: string;
  status: 'pending' | 'accepted' | 'declined';
  token: string;
  expires_at: string;
  created_at: string;
}

export interface ActivityLog {
  id: string;
  project_id?: string;
  user_id: string;
  action: string;
  entity_type?: string;
  entity_id?: string;
  details: Record<string, any>;
  created_at: string;
  profile?: Profile;
}

export interface AIConversation {
  id: string;
  user_id: string;
  project_id?: string;
  title: string;
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
  }>;
  model_name?: string;
  created_at: string;
  updated_at: string;
}

export interface AISuggestion {
  id: string;
  user_id: string;
  project_id?: string;
  conversation_id?: string;
  suggestion_type: string;
  suggestion_data: Record<string, any>;
  status: 'pending' | 'approved' | 'rejected';
  approved_at?: string;
  created_at: string;
}

export interface OpenRouterSettings {
  id: string;
  api_keys: string[];
  enabled_models: string[];
  model_display_names: Record<string, string>;
  created_by: string;
  created_at: string;
  updated_at: string;
}