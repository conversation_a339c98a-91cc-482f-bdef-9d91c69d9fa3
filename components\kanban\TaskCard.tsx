'use client';

import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Task } from '@/lib/supabase';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Calendar, 
  MessageSquare, 
  Paperclip, 
  AlertCircle,
  Clock,
  CheckSquare
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface TaskCardProps {
  task: Task;
}

const priorityColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  critical: 'bg-red-100 text-red-800',
};

const priorityIcons = {
  low: null,
  medium: null,
  high: AlertCircle,
  critical: AlertCircle,
};

export function TaskCard({ task }: TaskCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: task.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const PriorityIcon = priorityIcons[task.priority];
  const completedSubtasks = task.subtasks?.filter(st => st.completed).length || 0;
  const totalSubtasks = task.subtasks?.length || 0;

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`cursor-grab hover:shadow-md transition-shadow ${
        isDragging ? 'opacity-50 rotate-3' : ''
      }`}
      {...attributes}
      {...listeners}
    >
      <CardContent className="p-4">
        {/* Priority and Tags */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-1">
            <Badge
              variant="secondary"
              className={`text-xs ${priorityColors[task.priority]}`}
            >
              {PriorityIcon && <PriorityIcon className="h-3 w-3 mr-1" />}
              {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
            </Badge>
          </div>
          {task.due_date && (
            <div className="flex items-center text-xs text-gray-500">
              <Calendar className="h-3 w-3 mr-1" />
              {formatDistanceToNow(new Date(task.due_date), { addSuffix: true })}
            </div>
          )}
        </div>

        {/* Title */}
        <h4 className="font-medium text-gray-900 mb-2 line-clamp-2">
          {task.title}
        </h4>

        {/* Description */}
        {task.description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {task.description.replace(/<[^>]*>/g, '')} {/* Strip HTML tags for preview */}
          </p>
        )}

        {/* Tags */}
        {task.tags && task.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {task.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {task.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{task.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Progress and Stats */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-3">
            {/* Subtasks */}
            {totalSubtasks > 0 && (
              <div className="flex items-center">
                <CheckSquare className="h-3 w-3 mr-1" />
                <span>{completedSubtasks}/{totalSubtasks}</span>
              </div>
            )}

            {/* Comments */}
            <div className="flex items-center">
              <MessageSquare className="h-3 w-3 mr-1" />
              <span>0</span>
            </div>

            {/* Attachments */}
            {task.attachments && task.attachments.length > 0 && (
              <div className="flex items-center">
                <Paperclip className="h-3 w-3 mr-1" />
                <span>{task.attachments.length}</span>
              </div>
            )}

            {/* Time tracking */}
            {task.estimated_hours && (
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                <span>{task.actual_hours || 0}h/{task.estimated_hours}h</span>
              </div>
            )}
          </div>

          {/* Assignees */}
          {task.assignments && task.assignments.length > 0 && (
            <div className="flex -space-x-1">
              {task.assignments.slice(0, 3).map((assignment) => (
                <Avatar key={assignment.id} className="h-6 w-6 border-2 border-white">
                  <AvatarImage src={assignment.profile?.avatar_url} />
                  <AvatarFallback className="text-xs">
                    {assignment.profile?.full_name?.split(' ').map(n => n[0]).join('') || '?'}
                  </AvatarFallback>
                </Avatar>
              ))}
              {task.assignments.length > 3 && (
                <div className="h-6 w-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs text-gray-600">
                  +{task.assignments.length - 3}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Progress bar for subtasks */}
        {totalSubtasks > 0 && (
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-1">
              <div
                className="bg-blue-500 h-1 rounded-full transition-all"
                style={{
                  width: `${(completedSubtasks / totalSubtasks) * 100}%`,
                }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}